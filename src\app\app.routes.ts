import { Routes } from '@angular/router';
import { UsersListComponent } from './admin/pages/users/users-list/users-list.component';
import { UserCreateComponent } from './admin/pages/users/user-create/user-create.component';
import { UserEditComponent } from './admin/pages/users/user-edit/user-edit.component';
import { RolesListComponent } from './admin/pages/roles/roles-list/roles-list.component';
import { RoleCreateComponent } from './admin/pages/roles/role-create/role-create.component';
import { RoleEditComponent } from './admin/pages/roles/role-edit/role-edit.component';
import { RequestsListComponent } from './admin/pages/requests/requests-list/requests-list.component';
import { RegisterComponent } from './auth/register/register.component';
import { DashboardComponent } from './admin/pages/dashboard/dashboard.component';
import { AdminLayoutComponent } from './admin/layouts/admin-layout/admin-layout.component';
import { adminGuard } from './auth/admin.guard';
import { LoginComponent } from './auth/login/login.component';
import { InvestisseurLayoutComponent } from './investisseur/layouts/investisseur-layout/investisseur-layout.component';
import { PortefeuilleComponent } from './investisseur/pages/portefeuille/portefeuille.component';
import { DashboardInComponent } from './investisseur/pages/dashboard/dashboardIn.component';
import { OrdresComponent } from './investisseur/pages/ordres/ordres.component';
import { ActionsComponent } from './investisseur/pages/actions/actions.component';
import { ParametresComponent } from './investisseur/pages/parametres/parametres.component';
import { NotificationsComponent } from './investisseur/pages/notifications/notifications.component';
import { IntermediaireLayoutComponent } from './intermediaire/layouts/intermediaire-layout/intermediaire-layout.component';
import { DashboardBComponent } from './intermediaire/pages/dashboard-b/dashboard-b.component';
import { ClientsComponent } from './intermediaire/pages/clients/clients.component';
import { OrdreComponent } from './intermediaire/pages/ordre/ordre.component';
import { OperationsComponent } from './intermediaire/pages/operations/operations.component';

export const routes: Routes = [
  {
    path: 'admin',
    component: AdminLayoutComponent, // ✅ Wrapper

    children: [
      { path: 'dashboard', component: DashboardComponent },
      { path: 'users', component: UsersListComponent },
      { path: 'users/create', component: UserCreateComponent },
      { path: 'users/edit/:username', component: UserEditComponent },

      { path: 'roles', component: RolesListComponent },
      { path: 'roles/create', component: RoleCreateComponent },
      { path: 'roles/edit/:name', component: RoleEditComponent },

      { path: 'requests', component: RequestsListComponent },
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' }
    ]
  },
  {
    path: 'investisseur',
    component: InvestisseurLayoutComponent, // ✅ Wrapper

    children: [
      { path: 'dashboard', component: DashboardInComponent },
      { path: 'portefeuilles', component: PortefeuilleComponent },
      { path: 'ordres', component: OrdresComponent },
      { path: 'actions', component: ActionsComponent },
      { path: 'parametres', component: ParametresComponent },
      { path: 'notifications', component: NotificationsComponent },



    ]
  },
  {
    path: 'intermediaire',
    component: IntermediaireLayoutComponent, // ✅ Wrapper

    children: [
      { path: 'dashboard', component: DashboardBComponent },
      { path: 'clients', component: ClientsComponent },
      { path: 'ordre', component: OrdreComponent },
      { path: 'operations', component: OperationsComponent }


    ]
  },



  { path: 'register', component: RegisterComponent },
  { path: 'login', component: LoginComponent },

  { path: '', redirectTo: 'login', pathMatch: 'full' },
  { path: '**', redirectTo: 'login' } // si route inconnue


];