<!-- Container principal ultra-moderne avec particules flottantes -->
<div class="ultra-modern-container">
    <!-- Particules d'arrière-plan animées -->
    <div class="floating-particles">
        <div class="particle" *ngFor="let particle of [1,2,3,4,5,6,7,8]"></div>
    </div>

    <!-- Header ultra-moderne avec effets glassmorphism -->
    <div class="modern-header">
        <div class="header-background-effects">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
        </div>

        <div class="header-content">
            <div class="header-left">
                <div class="page-title">
                    <div class="title-icon-container">
                        <div class="title-icon">
                            <i class="pi pi-cog"></i>
                            <div class="icon-pulse"></div>
                        </div>
                        <div class="icon-glow"></div>
                    </div>
                    <div class="title-text">
                        <h1>
                            <span class="title-gradient">Opérations</span>
                            <span class="title-accent">en Cours</span>
                        </h1>
                        <p class="subtitle-enhanced">
                            <i class="pi pi-chart-line me-2"></i>
                            Suivi en temps réel des opérations d'exécution et analyse des ordres associés
                        </p>
                    </div>
                </div>
            </div>

            <div class="stats-cards-enhanced">
                <div class="stat-card-modern execution-card">
                    <div class="card-background">
                        <div class="card-pattern"></div>
                    </div>
                    <div class="stat-icon-modern execution">
                        <i class="pi pi-clock"></i>
                        <div class="icon-ring"></div>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number-animated">{{ executionGroups.length }}</div>
                        <div class="stat-label-modern">En Exécution</div>
                        <div class="stat-progress">
                            <div class="progress-bar execution-progress"></div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>

                <div class="stat-card-modern linked-card">
                    <div class="card-background">
                        <div class="card-pattern"></div>
                    </div>
                    <div class="stat-icon-modern linked">
                        <i class="pi pi-sitemap"></i>
                        <div class="icon-ring"></div>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number-animated">{{ getTotalOtherOperations() }}</div>
                        <div class="stat-label-modern">Opérations Liées</div>
                        <div class="stat-progress">
                            <div class="progress-bar linked-progress"></div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>

                <div class="stat-card-modern total-card">
                    <div class="card-background">
                        <div class="card-pattern"></div>
                    </div>
                    <div class="stat-icon-modern total">
                        <i class="pi pi-database"></i>
                        <div class="icon-ring"></div>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number-animated">{{ getTotalOrders() }}</div>
                        <div class="stat-label-modern">Ordres Totaux</div>
                        <div class="stat-progress">
                            <div class="progress-bar total-progress"></div>
                        </div>
                    </div>
                    <div class="card-shine"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal avec design futuriste -->
    <div class="main-content">
        <div class="content-section-enhanced">
            <div class="section-glow"></div>

            <!-- Barre d'actions ultra-moderne -->
            <div class="action-bar-futuristic">
                <div class="action-bar-left">
                    <div class="section-title-container">
                        <div class="title-icon-wrapper">
                            <i class="pi pi-list"></i>
                            <div class="title-icon-bg"></div>
                        </div>
                        <h2 class="section-title-enhanced">
                            <span class="title-main">Groupes d'Exécution</span>
                            <span class="title-sub">Analyse en temps réel</span>
                        </h2>
                    </div>

                    <!-- Indicateurs de statut en temps réel -->
                    <div class="status-indicators">
                        <div class="status-indicator active" [class.pulse]="loading">
                            <div class="indicator-dot"></div>
                            <span>Système Actif</span>
                        </div>
                        <div class="status-indicator" [class.connected]="!error">
                            <div class="indicator-dot"></div>
                            <span>{{ error ? 'Déconnecté' : 'Connecté' }}</span>
                        </div>
                    </div>
                </div>

                <div class="action-bar-right">
                    <div class="control-panel">
                        <!-- Bouton d'actualisation avec animation -->
                        <button class="futuristic-btn refresh-btn" (click)="refreshData()" [disabled]="loading"
                            [class.loading]="loading">
                            <div class="btn-background"></div>
                            <div class="btn-content">
                                <i class="pi pi-refresh" [class.spinning]="loading"></i>
                                <span>{{ loading ? 'Actualisation...' : 'Actualiser' }}</span>
                            </div>
                            <div class="btn-glow"></div>
                        </button>

                        <!-- Bouton de vue en grille -->
                        <button class="futuristic-btn view-btn" (click)="toggleView()">
                            <div class="btn-background"></div>
                            <div class="btn-content">
                                <i class="pi" [class]="viewMode === 'grid' ? 'pi-th-large' : 'pi-list'"></i>
                            </div>
                            <div class="btn-glow"></div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Zone de contenu avec design futuriste -->
            <div class="operations-content-enhanced">

                <!-- État de chargement futuriste -->
                <div *ngIf="loading" class="loading-container-futuristic">
                    <div class="loading-animation">
                        <div class="loading-orb">
                            <div class="orb-inner"></div>
                            <div class="orb-ring ring-1"></div>
                            <div class="orb-ring ring-2"></div>
                            <div class="orb-ring ring-3"></div>
                        </div>
                        <div class="loading-particles">
                            <div class="particle" *ngFor="let p of [1,2,3,4,5,6]"></div>
                        </div>
                    </div>
                    <div class="loading-text-enhanced">
                        <h3>Analyse en cours</h3>
                        <p>Traitement des données d'exécution...</p>
                        <div class="loading-progress">
                            <div class="progress-line"></div>
                        </div>
                    </div>
                </div>

                <!-- Message d'erreur futuriste -->
                <div *ngIf="error && !loading" class="error-container-futuristic">
                    <div class="error-card-enhanced">
                        <div class="error-background">
                            <div class="error-pattern"></div>
                        </div>
                        <div class="error-icon-container">
                            <i class="pi pi-exclamation-triangle error-icon-enhanced"></i>
                            <div class="error-icon-glow"></div>
                        </div>
                        <div class="error-content-enhanced">
                            <h3>Connexion Interrompue</h3>
                            <p>{{ error }}</p>
                            <button class="futuristic-btn retry-btn-enhanced" (click)="refreshData()">
                                <div class="btn-background"></div>
                                <div class="btn-content">
                                    <i class="pi pi-refresh me-2"></i>
                                    <span>Reconnecter</span>
                                </div>
                                <div class="btn-glow"></div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Liste des groupes d'exécution avec design futuriste -->
                <div *ngIf="!loading && !error" class="execution-groups-enhanced"
                    [class.grid-view]="viewMode === 'grid'">

                    <!-- Message si aucune opération -->
                    <div *ngIf="executionGroups.length === 0" class="empty-state">
                        <div class="empty-card">
                            <i class="pi pi-info-circle empty-icon"></i>
                            <h3>Aucune opération en cours</h3>
                            <p>Il n'y a actuellement aucune opération en cours d'exécution.</p>
                        </div>
                    </div>

                    <!-- Accordéon pour chaque groupe -->
                    <p-accordion *ngIf="executionGroups.length > 0" class="execution-accordion">
                        <p-accordionTab *ngFor="let group of executionGroups; let i = index" [selected]="i === 0">

                            <!-- En-tête de l'accordéon -->
                            <ng-template pTemplate="header">
                                <div class="accordion-header">
                                    <div class="operation-info">
                                        <div class="operation-badge">
                                            <i class="pi pi-cog me-2"></i>
                                            Opération {{ group.operationEnCours.id }}
                                        </div>
                                        <p-tag [value]="getStatusLabel(group.operationEnCours.statut)"
                                            [severity]="getStatusSeverity(group.operationEnCours.statut)"
                                            class="status-tag">
                                        </p-tag>
                                    </div>

                                    <div class="operation-details">
                                        <div class="detail-item">
                                            <span class="label">Quantité:</span>
                                            <span class="value">{{ group.operationEnCours.quantiteTotale }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="label">Montant:</span>
                                            <span class="value">{{ formatCurrency(group.operationEnCours.montantTotal)
                                                }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="label">Date:</span>
                                            <span class="value">{{ formatDate(group.operationEnCours.dateDeGeneration)
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>

                            <!-- Contenu de l'accordéon -->
                            <div class="accordion-content">

                                <!-- Section des ordres associés -->
                                <div class="section-card">
                                    <div class="section-header">
                                        <h4>
                                            <i class="pi pi-list me-2"></i>
                                            Ordres Associés ({{ group.ordresAssocies.length }})
                                        </h4>
                                    </div>

                                    <div class="ordres-grid">
                                        <div *ngFor="let ordre of group.ordresAssocies" class="ordre-card">
                                            <div class="ordre-header">
                                                <div class="ordre-badge" [ngClass]="getTypeClass(ordre.type)">
                                                    <i [class]="getTypeIcon(ordre.type)" class="me-1"></i>
                                                    {{ ordre.type }}
                                                </div>
                                                <p-tag [value]="getStatusLabel(ordre.statut)"
                                                    [severity]="getStatusSeverity(ordre.statut)">
                                                </p-tag>
                                            </div>

                                            <div class="ordre-details">
                                                <div class="detail-row">
                                                    <span class="label">ID:</span>
                                                    <span class="value">{{ ordre.id }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Action:</span>
                                                    <span class="value">{{ ordre.actionNom }} ({{ ordre.actionIsin
                                                        }})</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Quantité:</span>
                                                    <span class="value highlight">{{ ordre.quantite }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Prix Min/Max:</span>
                                                    <span class="value">{{ formatCurrency(ordre.prixMin) }} - {{
                                                        formatCurrency(ordre.prixMax) }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Investisseur:</span>
                                                    <span class="value">{{ ordre.investisseurUsername }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Date de validité:</span>
                                                    <span class="value">{{ formatDate(ordre.dateDeValidite) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Divider -->
                                <p-divider *ngIf="group.autresOperations.length > 0"></p-divider>

                                <!-- Section des autres opérations -->
                                <div *ngIf="group.autresOperations.length > 0" class="section-card">
                                    <div class="section-header">
                                        <h4>
                                            <i class="pi pi-history me-2"></i>
                                            Autres Opérations Traitées ({{ group.autresOperations.length }})
                                        </h4>
                                        <p class="section-description">
                                            Opérations déjà traitées qui partagent les mêmes ordres
                                        </p>
                                    </div>

                                    <div class="operations-grid">
                                        <div *ngFor="let operation of group.autresOperations" class="operation-card">
                                            <div class="operation-header">
                                                <div class="operation-badge">
                                                    <i class="pi pi-check-circle me-1"></i>
                                                    Op. {{ operation.id }}
                                                </div>
                                                <p-tag [value]="getStatusLabel(operation.statut)"
                                                    [severity]="getStatusSeverity(operation.statut)">
                                                </p-tag>
                                            </div>

                                            <div class="operation-details">
                                                <div class="detail-row">
                                                    <span class="label">Quantité:</span>
                                                    <span class="value highlight">{{ operation.quantiteTotale }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Montant:</span>
                                                    <span class="value">{{ formatCurrency(operation.montantTotal)
                                                        }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Commission:</span>
                                                    <span class="value">{{ formatCurrency(operation.commission)
                                                        }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Date:</span>
                                                    <span class="value">{{ formatDate(operation.dateDeGeneration)
                                                        }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </p-accordionTab>
                    </p-accordion>

                </div>
            </div>
        </div>
    </div>
</div>