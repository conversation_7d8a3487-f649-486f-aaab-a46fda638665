<!-- Container principal ultra-moderne -->
<div class="ultra-modern-container">

    <!-- Header ultra-moderne -->
    <div class="modern-header">
        <div class="header-content">
            <div class="header-left">
                <div class="page-title">
                    <div class="title-icon">
                        <i class="pi pi-cog"></i>
                    </div>
                    <div class="title-text">
                        <h1>Opérations en Cours</h1>
                        <p>Suivi des opérations en cours d'exécution et leurs ordres associés</p>
                    </div>
                </div>
            </div>

            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon execution">
                        <i class="pi pi-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ executionGroups.length }}</div>
                        <div class="stat-label">En Exécution</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon validés">
                        <i class="pi pi-refresh"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ getTotalOtherOperations() }}</div>
                        <div class="stat-label">Opérations Liées</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <div class="content-section">

            <!-- Barre d'actions -->
            <div class="action-bar">
                <div class="action-bar-left">
                    <h2 class="section-title">
                        <i class="pi pi-list me-2"></i>
                        Groupes d'Exécution
                    </h2>
                </div>

                <div class="action-bar-right">
                    <button class="modern-action-btn" (click)="refreshData()" [disabled]="loading">
                        <i class="pi pi-refresh me-2"></i>
                        Actualiser
                    </button>
                </div>
            </div>

            <!-- Zone de contenu -->
            <div class="operations-content">

                <!-- État de chargement -->
                <div *ngIf="loading" class="loading-container">
                    <p-progressSpinner styleClass="custom-spinner" strokeWidth="3" animationDuration="1s">
                    </p-progressSpinner>
                    <p class="loading-text">Chargement des opérations en cours...</p>
                </div>

                <!-- Message d'erreur -->
                <div *ngIf="error && !loading" class="error-container">
                    <div class="error-card">
                        <i class="pi pi-exclamation-triangle error-icon"></i>
                        <div class="error-content">
                            <h3>Erreur de chargement</h3>
                            <p>{{ error }}</p>
                            <button class="retry-btn" (click)="refreshData()">
                                <i class="pi pi-refresh me-2"></i>
                                Réessayer
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Liste des groupes d'exécution -->
                <div *ngIf="!loading && !error" class="execution-groups">

                    <!-- Message si aucune opération -->
                    <div *ngIf="executionGroups.length === 0" class="empty-state">
                        <div class="empty-card">
                            <i class="pi pi-info-circle empty-icon"></i>
                            <h3>Aucune opération en cours</h3>
                            <p>Il n'y a actuellement aucune opération en cours d'exécution.</p>
                        </div>
                    </div>

                    <!-- Accordéon pour chaque groupe -->
                    <p-accordion *ngIf="executionGroups.length > 0" class="execution-accordion">
                        <p-accordionTab *ngFor="let group of executionGroups; let i = index" [selected]="i === 0">

                            <!-- En-tête de l'accordéon -->
                            <ng-template pTemplate="header">
                                <div class="accordion-header">
                                    <div class="operation-info">
                                        <div class="operation-badge">
                                            <i class="pi pi-cog me-2"></i>
                                            Opération {{ group.operationEnCours.id }}
                                        </div>
                                        <p-tag [value]="getStatusLabel(group.operationEnCours.statut)"
                                            [severity]="getStatusSeverity(group.operationEnCours.statut)"
                                            class="status-tag">
                                        </p-tag>
                                    </div>

                                    <div class="operation-details">
                                        <div class="detail-item">
                                            <span class="label">Quantité:</span>
                                            <span class="value">{{ group.operationEnCours.quantiteTotale }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="label">Montant:</span>
                                            <span class="value">{{ formatCurrency(group.operationEnCours.montantTotal)
                                                }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="label">Date:</span>
                                            <span class="value">{{ formatDate(group.operationEnCours.dateDeGeneration)
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>

                            <!-- Contenu de l'accordéon -->
                            <div class="accordion-content">

                                <!-- Section des ordres associés -->
                                <div class="section-card">
                                    <div class="section-header">
                                        <h4>
                                            <i class="pi pi-list me-2"></i>
                                            Ordres Associés ({{ group.ordresAssocies.length }})
                                        </h4>
                                    </div>

                                    <div class="ordres-grid">
                                        <div *ngFor="let ordre of group.ordresAssocies" class="ordre-card">
                                            <div class="ordre-header">
                                                <div class="ordre-badge" [ngClass]="getTypeClass(ordre.type)">
                                                    <i [class]="getTypeIcon(ordre.type)" class="me-1"></i>
                                                    {{ ordre.type }}
                                                </div>
                                                <p-tag [value]="getStatusLabel(ordre.statut)"
                                                    [severity]="getStatusSeverity(ordre.statut)">
                                                </p-tag>
                                            </div>

                                            <div class="ordre-details">
                                                <div class="detail-row">
                                                    <span class="label">ID:</span>
                                                    <span class="value">{{ ordre.id }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Action:</span>
                                                    <span class="value">{{ ordre.actionNom }} ({{ ordre.actionIsin
                                                        }})</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Quantité:</span>
                                                    <span class="value highlight">{{ ordre.quantite }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Prix Min/Max:</span>
                                                    <span class="value">{{ formatCurrency(ordre.prixMin) }} - {{
                                                        formatCurrency(ordre.prixMax) }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Investisseur:</span>
                                                    <span class="value">{{ ordre.investisseurUsername }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Date de validité:</span>
                                                    <span class="value">{{ formatDate(ordre.dateDeValidite) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Divider -->
                                <p-divider *ngIf="group.autresOperations.length > 0"></p-divider>

                                <!-- Section des autres opérations -->
                                <div *ngIf="group.autresOperations.length > 0" class="section-card">
                                    <div class="section-header">
                                        <h4>
                                            <i class="pi pi-history me-2"></i>
                                            Autres Opérations Traitées ({{ group.autresOperations.length }})
                                        </h4>
                                        <p class="section-description">
                                            Opérations déjà traitées qui partagent les mêmes ordres
                                        </p>
                                    </div>

                                    <div class="operations-grid">
                                        <div *ngFor="let operation of group.autresOperations" class="operation-card">
                                            <div class="operation-header">
                                                <div class="operation-badge">
                                                    <i class="pi pi-check-circle me-1"></i>
                                                    Op. {{ operation.id }}
                                                </div>
                                                <p-tag [value]="getStatusLabel(operation.statut)"
                                                    [severity]="getStatusSeverity(operation.statut)">
                                                </p-tag>
                                            </div>

                                            <div class="operation-details">
                                                <div class="detail-row">
                                                    <span class="label">Quantité:</span>
                                                    <span class="value highlight">{{ operation.quantiteTotale }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Montant:</span>
                                                    <span class="value">{{ formatCurrency(operation.montantTotal)
                                                        }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Commission:</span>
                                                    <span class="value">{{ formatCurrency(operation.commission)
                                                        }}</span>
                                                </div>
                                                <div class="detail-row">
                                                    <span class="label">Date:</span>
                                                    <span class="value">{{ formatDate(operation.dateDeGeneration)
                                                        }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </p-accordionTab>
                    </p-accordion>

                </div>
            </div>
        </div>
    </div>
</div>