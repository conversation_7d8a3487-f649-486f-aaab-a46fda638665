/* ===== DESIGN ULTRA-MODERNE POUR OPERATIONS COMPONENT ===== */

/* Container principal ultra-moderne */
.ultra-modern-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    height: auto;
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: auto;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

/* Header ultra-moderne responsive */
.modern-header {
    background: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    color: white;
    padding: clamp(1rem, 4vw, 2rem) clamp(1rem, 5vw, 2.5rem);
    border-radius: 0 0 clamp(1rem, 3vw, 2rem) clamp(1rem, 3vw, 2rem);
    box-shadow: 0 10px 40px rgba(13, 84, 44, 0.3);
    margin-bottom: clamp(1rem, 3vw, 2rem);
    position: relative;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 1;
    flex-wrap: nowrap;
    gap: 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.page-title {
    display: flex;
    align-items: center;
    gap: clamp(0.75rem, 2vw, 1.5rem);
    flex-wrap: wrap;
}

.title-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.title-icon i {
    font-size: 2rem;
    color: white;
}

.title-text h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-text p {
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Cartes de statistiques responsive */
.stats-cards {
    display: flex;
    gap: clamp(0.5rem, 2vw, 1.5rem);
    flex-wrap: nowrap;
    justify-content: flex-end;
    flex-shrink: 0;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: clamp(0.5rem, 1.5vw, 1rem);
    padding: clamp(0.75rem, 2vw, 1.5rem);
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 1.5vw, 1rem);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.25);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-icon.validés {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.stat-icon.execution {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.stat-icon.executé {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Contenu principal responsive */
.main-content {
    overflow-x: auto;
    width: 100%;
    box-sizing: border-box;
    padding: 0 clamp(1rem, 3vw, 2.5rem);
}

.content-section {
    background: white;
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: visible;
    border: 1px solid rgba(226, 232, 240, 0.8);
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
}

/* Barre d'actions ultra-moderne responsive */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: clamp(1.5rem, 3vw, 2.5rem);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    flex-wrap: nowrap;
    gap: 2rem;
    width: 100%;
    box-sizing: border-box;
    position: relative;
}

.action-bar-left {
    display: flex;
    align-items: center;
    gap: clamp(1rem, 3vw, 2rem);
    flex: 1;
    flex-wrap: wrap;
}

.section-title {
    margin: 0;
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.action-bar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Bouton d'action moderne unifié et responsive */
.modern-action-btn {
    position: relative;
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 3vw, 2rem);
    background: linear-gradient(135deg, #0d542c, #118749);
    color: white;
    border: none;
    border-radius: clamp(0.5rem, 1.5vw, 0.75rem);
    font-weight: 600;
    font-size: clamp(0.8rem, 1.5vw, 0.95rem);
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(13, 84, 44, 0.3);
    white-space: nowrap;
    box-sizing: border-box;
}

.modern-action-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 84, 44, 0.4);
    background: linear-gradient(135deg, #0f5d31, #13a555);
}

.modern-action-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(13, 84, 44, 0.3);
}

.modern-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(13, 84, 44, 0.2);
}

/* Effet de brillance sur le bouton */
.modern-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modern-action-btn:hover:not(:disabled)::before {
    left: 100%;
}

/* Zone de contenu des opérations */
.operations-content {
    padding: clamp(1rem, 3vw, 2.5rem);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* États de chargement et d'erreur */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1.5rem;
}

::ng-deep .custom-spinner .p-progress-spinner-circle {
    stroke: #0d542c;
    stroke-width: 3;
}

.loading-text {
    font-size: 1.1rem;
    color: #64748b;
    font-weight: 500;
    margin: 0;
}

.error-container {
    display: flex;
    justify-content: center;
    padding: 2rem;
}

.error-card {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #fca5a5;
    border-radius: 1rem;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    max-width: 500px;
    width: 100%;
}

.error-icon {
    font-size: 2rem;
    color: #dc2626;
    flex-shrink: 0;
}

.error-content h3 {
    margin: 0 0 0.5rem 0;
    color: #991b1b;
    font-size: 1.2rem;
    font-weight: 600;
}

.error-content p {
    margin: 0 0 1rem 0;
    color: #7f1d1d;
    font-size: 0.95rem;
}

.retry-btn {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* État vide */
.empty-state {
    display: flex;
    justify-content: center;
    padding: 4rem 2rem;
}

.empty-card {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #7dd3fc;
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.empty-icon {
    font-size: 3rem;
    color: #0284c7;
    margin-bottom: 1rem;
}

.empty-card h3 {
    margin: 0 0 0.5rem 0;
    color: #0c4a6e;
    font-size: 1.3rem;
    font-weight: 600;
}

.empty-card p {
    margin: 0;
    color: #075985;
    font-size: 1rem;
}

/* Styles pour l'accordéon d'exécution */
.execution-groups {
    flex: 1;
}

::ng-deep .execution-accordion .p-accordion-header {
    margin-bottom: 1rem;
}

::ng-deep .execution-accordion .p-accordion-header-link {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

::ng-deep .execution-accordion .p-accordion-header-link:hover {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

::ng-deep .execution-accordion .p-accordion-header-link:focus {
    box-shadow: 0 0 0 3px rgba(13, 84, 44, 0.1);
}

::ng-deep .execution-accordion .p-accordion-content {
    background: white;
    border: 1px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 1rem 1rem;
    padding: 0;
}

/* En-tête de l'accordéon */
.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    width: 100%;
    gap: 2rem;
    flex-wrap: wrap;
}

.operation-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.operation-badge {
    background: linear-gradient(135deg, #0d542c, #118749);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(13, 84, 44, 0.3);
}

.operation-details {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    flex: 1;
    justify-content: flex-end;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.detail-item .label {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.detail-item .value {
    font-size: 0.95rem;
    color: #1e293b;
    font-weight: 600;
}

/* Contenu de l'accordéon */
.accordion-content {
    padding: 0;
}

.section-card {
    padding: 2rem;
    border-bottom: 1px solid #f1f5f9;
}

.section-card:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-header h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.section-description {
    margin: 0;
    font-size: 0.95rem;
    color: #64748b;
    font-style: italic;
}

/* Grilles pour les ordres et opérations */
.ordres-grid,
.operations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Cartes d'ordre */
.ordre-card {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ordre-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #0d542c;
}

.ordre-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.ordre-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ordre-badge.type-achat {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.ordre-badge.type-vente {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.ordre-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
    flex-shrink: 0;
}

.detail-row .value {
    font-size: 0.9rem;
    color: #1e293b;
    font-weight: 600;
    text-align: right;
    word-break: break-word;
}

.detail-row .value.highlight {
    color: #0d542c;
    font-weight: 700;
}

/* Cartes d'opération */
.operation-card {
    background: linear-gradient(135deg, #fefefe, #f8fafc);
    border: 1px solid #e2e8f0;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.operation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.operation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.operation-card .operation-badge {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.operation-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Styles pour les tags de statut */
::ng-deep .status-tag .p-tag {
    font-weight: 600;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1199px) {
    .stats-cards {
        flex-wrap: wrap;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .operation-details {
        justify-content: flex-start;
    }

    .detail-item {
        align-items: flex-start;
    }
}

@media (max-width: 768px) {
    .modern-header {
        padding: 1.5rem 1rem;
        border-radius: 0 0 1rem 1rem;
    }

    .title-text h1 {
        font-size: 2rem;
    }

    .title-text p {
        font-size: 1rem;
    }

    .main-content {
        padding: 0 1rem;
    }

    .action-bar {
        padding: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .action-bar-right {
        width: 100%;
        justify-content: flex-end;
    }

    .operations-content {
        padding: 1.5rem;
    }

    .accordion-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .operation-details {
        width: 100%;
        justify-content: space-between;
    }

    .ordres-grid,
    .operations-grid {
        grid-template-columns: 1fr;
    }

    .section-card {
        padding: 1.5rem;
    }
}

@media (max-width: 575px) {
    .modern-header {
        padding: 1rem;
    }

    .title-icon {
        width: 3rem;
        height: 3rem;
    }

    .title-icon i {
        font-size: 1.5rem;
    }

    .title-text h1 {
        font-size: 1.8rem;
    }

    .stats-cards {
        width: 100%;
        justify-content: space-between;
    }

    .stat-card {
        flex: 1;
        min-width: 0;
    }

    .stat-content {
        align-items: center;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
        text-align: center;
    }

    .operation-details {
        flex-direction: column;
        gap: 1rem;
    }

    .detail-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .ordre-header,
    .operation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Animations d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.execution-groups {
    animation: fadeInUp 0.6s ease-out;
}

.ordre-card,
.operation-card {
    animation: fadeInUp 0.4s ease-out;
}

.ordre-card:nth-child(2) {
    animation-delay: 0.1s;
}

.ordre-card:nth-child(3) {
    animation-delay: 0.2s;
}

.operation-card:nth-child(2) {
    animation-delay: 0.1s;
}

.operation-card:nth-child(3) {
    animation-delay: 0.2s;
}