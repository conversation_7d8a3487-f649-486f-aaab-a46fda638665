/* Variables CSS pour l'interface investisseur */
:root {
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --secondary-color: #f8fafc;
  --accent-color: #dbeafe;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --border-color: #e2e8f0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.16);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --gradient-bg: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* ===== SIDEBAR ULTRA-MODERNE INVESTISSEUR ===== */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.sidebar.collapsed {
  width: 80px;
}

/* En-tête de la sidebar */
.sidebar-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background:  linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius:  12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.brand-name {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 0.8rem;
  color: #1e3a8a;
  font-weight: 600;
}

.sidebar-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
}

.sidebar-toggle:hover {
  background: #dbeafe;
  color: #1e3a8a;
  transform: scale(1.1);
}

/* Menu de navigation */
.nav-menu {
  flex: 1;
  padding: 0 1rem;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
  padding: 0 1rem;
}

.nav-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-menu li {
  margin-bottom: 0.5rem;
  position: relative;
}

.nav-menu button {
  width: 100%;
  padding: 0.875rem 1rem;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #64748b;
  border-radius:  12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.nav-icon i {
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-text {
  font-weight: 500;
  flex: 1;
  text-align: left;
}

.nav-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: #1e3a8a;
  border-radius: 2px;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* États des boutons de navigation */
.nav-menu button:hover {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.08), rgba(59, 130, 246, 0.08));
  color: #1e3a8a;
  transform: translateX(4px);
}

.nav-menu li.active button {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.12), rgba(59, 130, 246, 0.12));
  color: #1e3a8a;
  font-weight: 600;
}

.nav-menu li.active .nav-indicator {
  opacity: 1;
}

/* Badge de notification */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* Pied de la sidebar */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  margin-top: auto;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius:  12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-profile:hover {
  background: #dbeafe;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background:  linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.user-role {
  font-size: 0.75rem;
  color: #1e3a8a;
  font-weight: 500;
}

/* ===== CONTENU PRINCIPAL ===== */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* ===== NAVBAR ULTRA-MODERNE INVESTISSEUR ===== */
.navbar {
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 999;
}

/* Section gauche de la navbar */
.navbar-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
}

.breadcrumb i {
  color: #1e3a8a;
}

.breadcrumb-separator {
  color: #94a3b8;
  margin: 0 0.25rem;
}

.current-page {
  color: #1e293b;
  font-weight: 600;
}

.user-greeting {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.greeting-text {
  font-size: 0.9rem;
  color: #64748b;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background: #10b981;
}

/* Section centre de la navbar */
.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 500px;
  margin: 0 2rem;
}

.search-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid #e2e8f0;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  color: #1e293b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.search-input:focus {
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
  background: white;
}

.search-input::placeholder {
  color: #94a3b8;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 1rem;
  pointer-events: none;
}

/* Section droite de la navbar */
.navbar-right {
  display: flex;
  align-items: center;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* Statut du marché */
.market-status {
  display: flex;
  align-items: center;
}

.market-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.market-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.market-text {
  font-size: 0.8rem;
  color: #10b981;
  font-weight: 600;
}

/* Bouton de notification */
.action-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: #f8fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
  position: relative;
}

.action-btn:hover {
  background: #dbeafe;
  color: #1e3a8a;
  transform: scale(1.05);
}

.notification-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

.notification-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ef4444;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Menu utilisateur */
.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 25px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.user-menu:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.user-avatar-nav {
  width: 36px;
  height: 36px;
  background:  linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.user-info-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-name-nav {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
}

.dropdown-icon {
  color: #64748b;
  font-size: 0.8rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-menu:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* Dropdown du menu utilisateur */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border-radius:  12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16);
  border: 1px solid #e2e8f0;
  min-width: 200px;
  overflow: hidden;
  z-index: 1000;
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  color: #64748b;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid #e2e8f0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f8fafc;
  color: #1e293b;
}

.dropdown-item i {
  font-size: 1rem;
  width: 20px;
}

.dropdown-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 0.5rem 0;
}

.logout-item {
  color: #ef4444;
}

.logout-item:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* Contenu principal */
.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: transparent;
}

/* ===== STYLES RESPONSIFS INVESTISSEUR ===== */

/* Tablettes */
@media (max-width: 1024px) {
  .sidebar {
    width: 260px;
  }

  .navbar-center {
    margin: 0 1rem;
  }

  .search-container {
    max-width: 300px;
  }

  .navbar-actions {
    gap: 1rem;
  }
}

/* Petites tablettes */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .navbar {
    padding: 0 1rem;
  }

  .navbar-left {
    gap: 1rem;
  }

  .navbar-center {
    display: none;
  }

  .user-greeting {
    display: none;
  }

  .breadcrumb {
    font-size: 0.8rem;
  }

  .market-status {
    display: none;
  }

  .user-info-nav {
    display: none;
  }

  .content {
    padding: 1rem;
  }
}

/* Mobiles */
@media (max-width: 480px) {
  .navbar {
    height: 60px;
    padding: 0 0.75rem;
  }

  .navbar-left {
    flex: 1;
  }

  .breadcrumb {
    font-size: 0.75rem;
  }

  .current-page {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .navbar-actions {
    gap: 0.5rem;
  }

  .action-btn {
    width: 36px;
    height: 36px;
  }

  .user-menu {
    padding: 0.25rem;
  }

  .user-avatar-nav {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .content {
    padding: 0.75rem;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .logo-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .brand-name {
    font-size: 1rem;
  }

  .brand-subtitle {
    font-size: 0.7rem;
  }

  .nav-menu button {
    padding: 0.75rem;
    font-size: 0.85rem;
  }

  .nav-section-title {
    font-size: 0.7rem;
    padding: 0 0.75rem;
  }
}

/* Mode sombre spécifique investisseur */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #3b82f6;
    --primary-light: #60a5fa;
    --primary-dark: #1e40af;
    --secondary-color: #1e293b;
    --accent-color: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-light: #94a3b8;
    --border-color: #334155;
    --success-color: #34d399;
    --danger-color: #f87171;
    --warning-color: #fbbf24;
  }

  .layout {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  .sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    border-right-color: rgba(255, 255, 255, 0.1);
  }

  .navbar {
    background: rgba(30, 41, 59, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .search-input {
    background: rgba(51, 65, 85, 0.9);
    border-color: #334155;
    color: #1e293b;
  }

  .search-input:focus {
    background: #334155;
    border-color: #1e3a8a;
  }

  .market-indicator {
    background: rgba(52, 211, 153, 0.1);
    border-color: rgba(52, 211, 153, 0.2);
  }
}

/* Animations et transitions */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}