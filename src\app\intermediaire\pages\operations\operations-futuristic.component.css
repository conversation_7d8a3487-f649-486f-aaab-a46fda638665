/* ===== STYLES FUTURISTES AVANCÉS POUR OPERATIONS COMPONENT ===== */

/* Section de contenu futuriste */
.content-section-enhanced {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    overflow: visible;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
}

.section-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #0d542c, #118749, #3b82f6);
    border-radius: clamp(0.75rem, 2vw, 1.5rem);
    opacity: 0.1;
    z-index: -1;
    animation: glowPulse 4s ease-in-out infinite;
}

@keyframes glowPulse {

    0%,
    100% {
        opacity: 0.1;
        transform: scale(1);
    }

    50% {
        opacity: 0.2;
        transform: scale(1.01);
    }
}

/* Barre d'actions futuriste */
.action-bar-futuristic {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: clamp(1.5rem, 3vw, 2.5rem);
    background:
        linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    flex-wrap: nowrap;
    gap: 2rem;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    backdrop-filter: blur(10px);
}

.section-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon-wrapper {
    position: relative;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.title-icon-wrapper i {
    font-size: 1.5rem;
    color: #0d542c;
    z-index: 2;
    position: relative;
}

.title-icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0d542c, #118749);
    border-radius: 0.75rem;
    opacity: 0.1;
    animation: iconBgPulse 3s ease-in-out infinite;
}

@keyframes iconBgPulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.1;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.2;
    }
}

.section-title-enhanced {
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.title-main {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 800;
    color: #1e293b;
    background: linear-gradient(135deg, #1e293b 0%, #0d542c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-sub {
    font-size: 0.9rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Indicateurs de statut en temps réel */
.status-indicators {
    display: flex;
    gap: 1rem;
    margin-left: 2rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #64748b;
    transition: all 0.3s ease;
}

.status-indicator.active {
    color: #059669;
    border-color: rgba(16, 185, 129, 0.3);
    background: rgba(16, 185, 129, 0.1);
}

.status-indicator.connected {
    color: #0d542c;
    border-color: rgba(13, 84, 44, 0.3);
    background: rgba(13, 84, 44, 0.1);
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: dotPulse 2s ease-in-out infinite;
}

.status-indicator.pulse .indicator-dot {
    animation: dotPulse 1s ease-in-out infinite;
}

@keyframes dotPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* Panneau de contrôle */
.control-panel {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Boutons futuristes */
.futuristic-btn {
    position: relative;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.futuristic-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0d542c, #118749);
    transition: all 0.3s ease;
    z-index: 1;
}

.refresh-btn .btn-background {
    background: linear-gradient(135deg, #0d542c, #118749);
}

.view-btn .btn-background {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.btn-content {
    position: relative;
    z-index: 2;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.futuristic-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.futuristic-btn:hover:not(:disabled) .btn-glow {
    opacity: 1;
}

.futuristic-btn:hover:not(:disabled) .btn-background {
    transform: scale(1.05);
}

.futuristic-btn:active:not(:disabled) {
    transform: translateY(0);
}

/* Animation de rotation pour l'icône de refresh */
.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* État de chargement du bouton */
.futuristic-btn.loading .btn-background {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    animation: loadingShimmer 1.5s ease-in-out infinite;
}

@keyframes loadingShimmer {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* ===== ÉTATS DE CHARGEMENT ET D'ERREUR FUTURISTES ===== */

/* Zone de contenu futuriste */
.operations-content-enhanced {
    padding: clamp(1rem, 3vw, 2.5rem);
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* État de chargement futuriste */
.loading-container-futuristic {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 2rem;
    min-height: 400px;
}

.loading-animation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-orb {
    position: relative;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.orb-inner {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #0d542c, #118749);
    border-radius: 50%;
    animation: orbPulse 2s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(13, 84, 44, 0.5);
}

.orb-ring {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    animation: ringRotate 3s linear infinite;
}

.ring-1 {
    width: 60px;
    height: 60px;
    border-color: rgba(13, 84, 44, 0.6) transparent rgba(13, 84, 44, 0.6) transparent;
    animation-duration: 2s;
}

.ring-2 {
    width: 80px;
    height: 80px;
    border-color: transparent rgba(17, 135, 73, 0.4) transparent rgba(17, 135, 73, 0.4);
    animation-duration: 3s;
    animation-direction: reverse;
}

.ring-3 {
    width: 100px;
    height: 100px;
    border-color: rgba(59, 130, 246, 0.3) transparent rgba(59, 130, 246, 0.3) transparent;
    animation-duration: 4s;
}

@keyframes orbPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }
}

@keyframes ringRotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.loading-particles {
    position: absolute;
    width: 200px;
    height: 200px;
    pointer-events: none;
}

.loading-particles .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #0d542c, #118749);
    border-radius: 50%;
    animation: particleFloat 3s ease-in-out infinite;
}

.loading-particles .particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.loading-particles .particle:nth-child(2) {
    top: 20%;
    right: 20%;
    animation-delay: 0.5s;
}

.loading-particles .particle:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 1s;
}

.loading-particles .particle:nth-child(4) {
    bottom: 20%;
    right: 20%;
    animation-delay: 1.5s;
}

.loading-particles .particle:nth-child(5) {
    top: 50%;
    left: 10%;
    animation-delay: 2s;
}

.loading-particles .particle:nth-child(6) {
    top: 50%;
    right: 10%;
    animation-delay: 2.5s;
}

@keyframes particleFloat {

    0%,
    100% {
        transform: translateY(0) scale(1);
        opacity: 0.6;
    }

    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

.loading-text-enhanced {
    text-align: center;
    max-width: 400px;
}

.loading-text-enhanced h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    background: linear-gradient(135deg, #1e293b 0%, #0d542c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-text-enhanced p {
    margin: 0 0 1.5rem 0;
    color: #64748b;
    font-size: 1rem;
    font-weight: 500;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(13, 84, 44, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.progress-line {
    height: 100%;
    background: linear-gradient(90deg, #0d542c, #118749, #3b82f6);
    border-radius: 2px;
    animation: progressMove 2s ease-in-out infinite;
}

@keyframes progressMove {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(200px);
    }
}

/* État d'erreur futuriste */
.error-container-futuristic {
    display: flex;
    justify-content: center;
    padding: 3rem 2rem;
    min-height: 300px;
    align-items: center;
}

.error-card-enhanced {
    position: relative;
    background:
        linear-gradient(135deg, rgba(254, 242, 242, 0.95) 0%, rgba(254, 226, 226, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(252, 165, 165, 0.3);
    border-radius: 1.5rem;
    padding: 2.5rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    max-width: 600px;
    width: 100%;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(220, 38, 38, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.error-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: 1;
}

.error-pattern {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(220, 38, 38, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(239, 68, 68, 0.1) 0%, transparent 50%);
    animation: errorPatternMove 8s ease-in-out infinite;
}

@keyframes errorPatternMove {

    0%,
    100% {
        transform: translate(0, 0) rotate(0deg);
    }

    50% {
        transform: translate(5px, -5px) rotate(1deg);
    }
}

.error-icon-container {
    position: relative;
    flex-shrink: 0;
    z-index: 2;
}

.error-icon-enhanced {
    font-size: 3rem;
    color: #dc2626;
    filter: drop-shadow(0 4px 8px rgba(220, 38, 38, 0.3));
    animation: errorIconPulse 2s ease-in-out infinite;
}

.error-icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(220, 38, 38, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: errorGlow 3s ease-in-out infinite;
}

@keyframes errorIconPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

@keyframes errorGlow {

    0%,
    100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

.error-content-enhanced {
    z-index: 2;
    position: relative;
}

.error-content-enhanced h3 {
    margin: 0 0 0.75rem 0;
    color: #991b1b;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #991b1b 0%, #dc2626 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.error-content-enhanced p {
    margin: 0 0 1.5rem 0;
    color: #7f1d1d;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
}

.retry-btn-enhanced {
    min-width: 140px;
}

.retry-btn-enhanced .btn-background {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Groupes d'exécution améliorés */
.execution-groups-enhanced {
    flex: 1;
    position: relative;
}

.execution-groups-enhanced.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* Styles d'accordéon futuristes */
::ng-deep .execution-accordion .p-accordion-header {
    margin-bottom: 1.5rem;
}

::ng-deep .execution-accordion .p-accordion-header-link {
    background:
        linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.3);
    border-radius: 1.25rem;
    padding: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}

::ng-deep .execution-accordion .p-accordion-header-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(13, 84, 44, 0.05), transparent);
    transition: left 0.6s ease;
}

::ng-deep .execution-accordion .p-accordion-header-link:hover::before {
    left: 100%;
}

::ng-deep .execution-accordion .p-accordion-header-link:hover {
    background:
        linear-gradient(135deg, rgba(241, 245, 249, 0.95) 0%, rgba(226, 232, 240, 0.95) 100%);
    transform: translateY(-2px);
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(13, 84, 44, 0.1);
}

::ng-deep .execution-accordion .p-accordion-header-link:focus {
    box-shadow:
        0 0 0 3px rgba(13, 84, 44, 0.1),
        0 4px 20px rgba(0, 0, 0, 0.05);
}

::ng-deep .execution-accordion .p-accordion-content {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.3);
    border-top: none;
    border-radius: 0 0 1.25rem 1.25rem;
    padding: 0;
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}