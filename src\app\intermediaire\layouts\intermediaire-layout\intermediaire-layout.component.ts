import { Component, OnInit } from '@angular/core';

import { CommonModule } from '@angular/common';
import { Router, RouterLinkActive, RouterOutlet } from '@angular/router';
import { AuthService } from '../../../auth/auth.service';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MenuItem } from 'primeng/api';
import { PanelMenuModule } from 'primeng/panelmenu';
import { ButtonModule } from 'primeng/button';
@Component({
  selector: 'app-intermediaire-layout',
  standalone: true,
  imports: [RouterOutlet, MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatListModule,
    MatInputModule,
    MatButtonModule, RouterLinkActive, CommonModule, PanelMenuModule,
    ButtonModule,],
  templateUrl: './intermediaire-layout.component.html',
  styleUrl: './intermediaire-layout.component.css'
})
export class IntermediaireLayoutComponent implements OnInit {
  username: string = '';
  menuItems: MenuItem[] = [];
  sidebarCollapsed: boolean = false;
  userMenuOpen: boolean = false;
  notificationCount: number = 3; // Exemple de compteur de notifications


  constructor(private authService: AuthService, private router: Router) { }

  isActiveRoute(route: string): boolean {
    return this.router.url === `/intermediaire/${route}`;
  }

  ngOnInit(): void {
    this.username = this.authService.getUsername();
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  toggleUserMenu(): void {
    this.userMenuOpen = !this.userMenuOpen;
  }

  getCurrentPageTitle(): string {
    const url = this.router.url;
    if (url.includes('dashboard')) return 'Dashboard';
    if (url.includes('clients')) return 'Clients';
    if (url.includes('ordre')) return 'Ordres';
     if (url.includes('operations')) return 'Opérations';

    if (url.includes('notifications')) return 'Notifications';
    if (url.includes('parametres')) return 'Paramètres';
    return 'Accueil';
  }

  logClick() {
    console.log('Clicked on Portefeuille');
  }

  navigateTo(path: string) {
    this.router.navigate([`/intermediaire/${path}`]);
    this.userMenuOpen = false; // Fermer le menu utilisateur après navigation
  }

  logout(): void {
    this.authService.logout();
  }

}

