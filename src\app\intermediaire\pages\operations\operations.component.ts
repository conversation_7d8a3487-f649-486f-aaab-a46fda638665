import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { AccordionModule } from 'primeng/accordion';
import { DividerModule } from 'primeng/divider';
import { IntermediaireService } from '../../services/intermediaire.service';
import { OperationExecutionGroupDTO } from '../../models/operation-simulation';

@Component({
  selector: 'app-operations',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    CardModule,
    TagModule,
    ButtonModule,
    ProgressSpinnerModule,
    AccordionModule,
    DividerModule
  ],
  templateUrl: './operations.component.html',
  styleUrl: './operations.component.css'
})
export class OperationsComponent implements OnInit {
  executionGroups: OperationExecutionGroupDTO[] = [];
  loading = true;
  error: string | null = null;

  constructor(private intermediaireService: IntermediaireService) { }

  ngOnInit(): void {
    this.loadExecutionGroups();
  }

  loadExecutionGroups(): void {
    this.loading = true;
    this.error = null;

    this.intermediaireService.getOperationExecutionGroups().subscribe({
      next: (groups) => {
        this.executionGroups = groups;
        this.loading = false;
        console.log('Groupes d\'exécution chargés:', groups);
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des opérations en cours d\'exécution';
        this.loading = false;
        console.error('Erreur:', error);
      }
    });
  }

  getStatusSeverity(statut: string): 'success' | 'info' | 'warning' | 'danger' {
    switch (statut) {
      case 'EN_EXECUTION': return 'warning';
      case 'TRAITE': return 'success';
      case 'VALIDE': return 'info';
      case 'ANNULE': return 'danger';
      default: return 'info';
    }
  }

  getStatusLabel(statut: string): string {
    switch (statut) {
      case 'EN_EXECUTION': return 'En Exécution';
      case 'TRAITE': return 'Traité';
      case 'VALIDE': return 'Validé';
      case 'ANNULE': return 'Annulé';
      default: return statut;
    }
  }

  getTypeIcon(type: string): string {
    return type === 'ACHAT' ? 'pi pi-arrow-down' : 'pi pi-arrow-up';
  }

  getTypeClass(type: string): string {
    return type === 'ACHAT' ? 'type-achat' : 'type-vente';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 2
    }).format(amount);
  }

  refreshData(): void {
    this.loadExecutionGroups();
  }

  getTotalOtherOperations(): number {
    return this.executionGroups.reduce((total, group) => total + group.autresOperations.length, 0);
  }
}
