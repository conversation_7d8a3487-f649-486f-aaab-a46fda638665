import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AjoutOrdreRequest, KpiOrdreResponse, NotificationDTO, OrdreResponseDTO, UpdateOrdreRequest, VenteTitreRequest } from '../models/ordre';
import { Observable } from 'rxjs';
import { OrdreProposeResponseDTO } from '../../intermediaire/models/OrdreP';

@Injectable({
  providedIn: 'root'
})
export class OrdreService {
  private apiUrl = 'http://localhost:8080/api/investisseur';

  constructor(private http: HttpClient) {}
   passerOrdre(req: AjoutOrdreRequest): Observable<OrdreResponseDTO> {
    return this.http.post<OrdreResponseDTO>(`${this.apiUrl}/passer_ordre`, req);
  }
  getMyOrdres(): Observable<OrdreResponseDTO[]> {
      return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/mesOrdres`);
    }
 getMesOrdresAvecStatut(statut: string): Observable<OrdreResponseDTO[]> {
  return this.http.get<OrdreResponseDTO[]>(`${this.apiUrl}/Ordres_statut?statut=${statut}`);
}
getOrdresEnAttentePourMoi(): Observable<OrdreProposeResponseDTO[]> {
  return this.http.get<OrdreProposeResponseDTO[]>(`${this.apiUrl}/ordres-attente`);
}
modifierOrdre(id: string, data: UpdateOrdreRequest): Observable<OrdreProposeResponseDTO> {
  return this.http.put<OrdreProposeResponseDTO>(`${this.apiUrl}/update_ordre/${id}`, data);
}
annulerOrdre(id: string): Observable<OrdreProposeResponseDTO> {
  return this.http.put<OrdreProposeResponseDTO>(`${this.apiUrl}/annuler_ordre/${id}`, {});
}
getOrdreKpi(start: string, end: string): Observable<KpiOrdreResponse> {
  return this.http.get<KpiOrdreResponse>(
    `${this.apiUrl}/ordresKPI?start=${start}&end=${end}`
  );
}
vendreTitre(request: VenteTitreRequest): Observable<OrdreResponseDTO> {
    return this.http.post<OrdreResponseDTO>(`${this.apiUrl}/vente`, request);
  }
  getMesNotifications(): Observable<NotificationDTO[]> {
    return this.http.get<NotificationDTO[]>(`${this.apiUrl}/notifications`);
  }
  marquerCommeLue(notificationId: string): Observable<void> {
  return this.http.put<void>(`${this.apiUrl}/notifications/${notificationId}/lire`, {});
}
getNombreNotificationsNonLuesPourUtilisateurConnecte(): Observable<number> {
  return this.http.get<number>(`${this.apiUrl}/notifications/non-lues/count`);
}


}
