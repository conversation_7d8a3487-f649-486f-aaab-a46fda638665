<!-- Container principal ultra-moderne -->
<div class="ultra-modern-container">

  <!-- Header avec navigation et statistiques -->
  <div class="modern-header">
    <div class="header-content">
      <div class="header-left">
        <div class="page-title">
          <div class="title-icon">
            <i class="pi pi-chart-line"></i>
          </div>
          <div class="title-text">
            <h1>Gestion des Ordres</h1>
            <p>Tableau de bord intermédiaire</p>
          </div>
        </div>
      </div>

      <div class="header-right">
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon validés">
              <i class="pi pi-check-circle"></i>
            </div>
            <div class="stat-content">
              <span class="stat-number">{{ ordresValides.length }}</span>
              <span class="stat-label">Validés</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon execution">
              <i class="pi pi-spin pi-cog"></i>
            </div>
            <div class="stat-content">
              <span class="stat-number">{{ ordresEnExecution.length }}</span>
              <span class="stat-label">En cours</span>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon executé">
              <i class="pi pi-send"></i>
            </div>
            <div class="stat-content">
              <span class="stat-number">{{ ordresExecute.length }}</span>
              <span class="stat-label">Exécutés</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation par onglets ultra-moderne -->
  <div class="modern-tabs-container">
    <p-tabMenu [model]="items" [(activeItem)]="activeItem"  (onTabChange)="onTabChange($event)"
      styleClass="ultra-modern-tabs"></p-tabMenu>
  </div>

  <!-- Contenu principal -->
  <div class="main-content">
    <div *ngIf="activeItem.label === 'Ordres validés'" class="content-section" >

      <!-- Barre d'actions moderne -->
      <div *ngIf="!affichageOrdresSimilaires">

        <div class="action-bar-right">
          <!-- Résumé de sélection avec animation -->
          <div class="selection-summary" *ngIf="selectedOrdres.length > 0" [@slideInFromRight]>
            <div class="selection-badge">
              <i class="pi pi-check-circle"></i>
              <span>{{ selectedOrdres.length }} sélectionné(s)</span>
            </div>
            <div class="selection-total">
              <span>Total: {{ getTotalQuantite() }} unités</span>
            </div>
          </div>

          <!-- Bouton traiter opération simplifié -->
          <button type="button" class="modern-action-btn" [disabled]="selectedOrdres.length === 0"
            [class.pulse]="selectedOrdres.length > 0" (click)="traiterOperation()">
            <div class="btn-content">
              <i class="pi pi-cog"></i>
              <span>Traiter l'opération</span>
            </div>
            <div class="btn-glow"></div>
          </button>
        </div>
      </div>

      <!-- Bouton de retour moderne pour les ordres similaires -->
      <div *ngIf="affichageOrdresSimilaires" class="similar-orders-header">
        <button type="button" class="back-button" (click)="restaurerOrdresOriginaux()">
          <i class="pi pi-arrow-left"></i>
          <span>Retour aux ordres validés</span>
        </button>
        <button type="button" class="modern-action-btn" [disabled]="selectedOrdres.length === 0"
          [class.pulse]="selectedOrdres.length > 0" (click)="traiterOperation()">
          <div class="btn-content">
            <i class="pi pi-cog"></i>
            <span>Traiter l'opération</span>
          </div>
          <div class="btn-glow"></div>
        </button>
      </div>

      <!-- Tableau ultra-moderne -->
      <div class="table-container">
        <p-table [value]="ordresValides" [paginator]="true" [rows]="7" filterDisplay="row" responsiveLayout="scroll"
          styleClass="ultra-modern-table" selectionMode="multiple" [selection]="selectedOrdres"
          (selectionChange)="onSelectionChange($event)" dataKey="id" [loading]="false" [scrollable]="false">

          <ng-template pTemplate="header">
            <tr class="table-header-row">
              <th class="selection-column">
                <div class="header-cell">
                  <i class="pi pi-check-square header-icon"></i>
                </div>
              </th>
              <th pSortableColumn="actionNom" class="action-column">
                <div class="header-cell">
                  <i class="pi pi-building header-icon"></i>
                  <span class="header-text">Action</span>
                  <p-columnFilter field="actionNom" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
             
              <th pSortableColumn="investisseurUsername" class="investor-column">
                <div class="header-cell">
                  <i class="pi pi-user header-icon"></i>
                  <span class="header-text">Investisseur</span>
                  <p-columnFilter field="investisseurUsername" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="type" class="type-column">
                <div class="header-cell">
                  <i class="pi pi-tag header-icon"></i>
                  <span class="header-text">Type</span>
                  <p-columnFilter field="type" type="text" matchMode="equals" display="menu">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-dropdown [options]="[{label:'ACHAT', value:'ACHAT'}, {label:'VENTE', value:'VENTE'}]"
                        [ngModel]="value" (onChange)="filter($event.value)" placeholder="Type">
                      </p-dropdown>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="dateDeValidite" class="date-column">
                <div class="header-cell">
                  <i class="pi pi-calendar header-icon"></i>
                  <span class="header-text">Validité</span>
                  <p-columnFilter field="dateDeValidite" display="menu" [showOperator]="false" matchMode="between"
                    dataType="date">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-calendar selectionMode="range" [ngModel]="value" (onSelect)="filter($event)"
                        dateFormat="dd/mm/yy" placeholder="Plage de dates" showIcon="true" class="date-range-filter">
                      </p-calendar>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th class="quantity-column">
                <div class="header-cell">
                  <i class="pi pi-hashtag header-icon"></i>
                  <span class="header-text">Quantité</span>
                </div>
              </th>
              <th class="price-column">
                <div class="header-cell">
                  <i class="pi pi-dollar header-icon"></i>
                  <span class="header-text">Prix</span>
                </div>
              </th>
              <th class="all-or-nothing-column">
                <div class="header-cell">
                  <i class="pi pi-exclamation-triangle header-icon"></i>
                  <span class="header-text">T.O.R</span>
                </div>
              </th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-ordre let-rowIndex="rowIndex">
            <tr class="table-row" [class.selected-row]="selectedOrdres.includes(ordre)"
              [class.disabled-row]="isSelectionDisabled(ordre)">
              <td class="selection-cell">
                <div class="checkbox-container">
                  <p-tableCheckbox [value]="ordre" [disabled]="isSelectionDisabled(ordre)"></p-tableCheckbox>
                </div>
              </td>
              <td class="action-cell">
                <div class="cell-content">
                  <div class="primary-text">{{ ordre.actionNom }}</div>
                  <div class="secondary-text">{{ ordre.actionIsin }}</div>
                </div>
              </td>
              
              <td class="investor-cell">
                <div class="cell-content">
                  <div class="investor-info">
                    <div class="investor-avatar">
                      <i class="pi pi-user"></i>
                    </div>
                    <div class="investor-details">
                      <div class="investor-name">{{ ordre.investisseurUsername }}</div>
                    </div>
                  </div>
                </div>
              </td>
              <td class="type-cell">
                <div class="cell-content">
                  <div class="type-badge" [ngClass]="'type-' + ordre.type.toLowerCase()">
                    <i class="pi" [ngClass]="ordre.type === 'ACHAT' ? 'pi-arrow-up' : 'pi-arrow-down'"></i>
                    <span>{{ ordre.type }}</span>
                  </div>
                </div>
              </td>
              <td class="date-cell">
                <div class="cell-content">
                  <div class="date-info">
                    <i class="pi pi-calendar"></i>
                    <span>{{ ordre.dateDeValidite | date:'dd/MM/yyyy' }}</span>
                  </div>
                </div>
              </td>
              <td class="quantity-cell">
                <div class="cell-content">
                  <div class="quantity-badge">
                    <i class="pi pi-hashtag"></i>
                    <span class="quantity-number">{{ ordre.quantite }}</span>
                  </div>
                </div>
              </td>
              <td class="price-cell">
                <div class="cell-content">
                  <div class="price-info">
                    <div class="price-label" *ngIf="ordre.type === 'VENTE'">Min</div>
                    <div class="price-label" *ngIf="ordre.type === 'ACHAT'">Max</div>
                    <div class="price-value">
                      <span *ngIf="ordre.type === 'VENTE'">{{ ordre.prixMin | number:'1.2-2' }}</span>
                      <span *ngIf="ordre.type === 'ACHAT'">{{ ordre.prixMax | number:'1.2-2' }}</span>
                      <span class="currency">TND</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="all-or-nothing-cell">
                <div class="cell-content">
                  <div class="tor-indicator" [class.active]="ordre.toutOuRien">
                    <i class="pi" [ngClass]="ordre.toutOuRien ? 'pi-exclamation-triangle' : 'pi-minus'"></i>
                    <span>{{ ordre.toutOuRien ? 'OUI' : 'NON' }}</span>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template>

        </p-table>
      </div>
    </div>

    <div *ngIf="activeItem.label === 'Ordres en exécution'" class="content-section">
      <div class="table-container">
        <p-table [value]="ordresEnExecution" [paginator]="true" [rows]="8" filterDisplay="row"
          responsiveLayout="scroll" styleClass="ultra-modern-table" [loading]="false" [scrollable]="false">

          <ng-template pTemplate="header">
            <tr class="table-header-row">
              <th pSortableColumn="actionNom" class="action-column">
                <div class="header-cell">
                  <i class="pi pi-building header-icon"></i>
                  <span class="header-text">Action</span>
                  <p-columnFilter field="actionNom" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="investisseurUsername" class="investor-column">
                <div class="header-cell">
                  <i class="pi pi-user header-icon"></i>
                  <span class="header-text">Investisseur</span>
                  <p-columnFilter field="investisseurUsername" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="type" class="type-column">
                <div class="header-cell">
                  <i class="pi pi-tag header-icon"></i>
                  <span class="header-text">Type</span>
                  <p-columnFilter field="type" type="text" matchMode="equals" display="menu">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-dropdown [options]="[{label:'ACHAT', value:'ACHAT'}, {label:'VENTE', value:'VENTE'}]"
                        [ngModel]="value" (onChange)="filter($event.value)" placeholder="Type">
                      </p-dropdown>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="dateDeValidite" class="date-column">
                <div class="header-cell">
                  <i class="pi pi-calendar header-icon"></i>
                  <span class="header-text">Validité</span>
                  <p-columnFilter field="dateDeValidite" display="menu" [showOperator]="false" matchMode="between"
                    dataType="date">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-calendar selectionMode="range" [ngModel]="value" (onSelect)="filter($event)"
                        dateFormat="dd/mm/yy" placeholder="Plage de dates" showIcon="true" class="date-range-filter">
                      </p-calendar>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th class="quantity-column">
                <div class="header-cell">
                  <i class="pi pi-hashtag header-icon"></i>
                  <span class="header-text">Quantité</span>
                </div>
              </th>
              <th class="price-column">
                <div class="header-cell">
                  <i class="pi pi-dollar header-icon"></i>
                  <span class="header-text">Prix</span>
                </div>
              </th>
              <th class="all-or-nothing-column">
                <div class="header-cell">
                  <i class="pi pi-exclamation-triangle header-icon"></i>
                  <span class="header-text">T.O.R</span>
                </div>
              </th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-ordre let-rowIndex="rowIndex">
            <tr class="table-row">
              <td class="action-cell">
                <div class="cell-content">
                  <div class="primary-text">{{ ordre.actionNom }}</div>
                  <div class="secondary-text">{{ ordre.actionIsin }}</div>
                </div>
              </td>
              <td class="investor-cell">
                <div class="cell-content">
                  <div class="investor-info">
                    <div class="investor-avatar">
                      <i class="pi pi-user"></i>
                    </div>
                    <div class="investor-details">
                      <div class="investor-name">{{ ordre.investisseurUsername }}</div>
                    </div>
                  </div>
                </div>
              </td>
              <td class="type-cell">
                <div class="cell-content">
                  <div class="type-badge" [ngClass]="'type-' + ordre.type.toLowerCase()">
                    <i class="pi" [ngClass]="ordre.type === 'ACHAT' ? 'pi-arrow-up' : 'pi-arrow-down'"></i>
                    <span>{{ ordre.type }}</span>
                  </div>
                </div>
              </td>
              <td class="date-cell">
                <div class="cell-content">
                  <div class="date-info">
                    <i class="pi pi-calendar"></i>
                    <span>{{ ordre.dateDeValidite | date:'dd/MM/yyyy' }}</span>
                  </div>
                </div>
              </td>
              <td class="quantity-cell">
                <div class="cell-content">
                  <div class="quantity-badge">
                    <i class="pi pi-hashtag"></i>
                    <span class="quantity-number">{{ ordre.quantite }}</span>
                  </div>
                </div>
              </td>
              <td class="price-cell">
                <div class="cell-content">
                  <div class="price-info">
                    <div class="price-label" *ngIf="ordre.type === 'VENTE'">Min</div>
                    <div class="price-label" *ngIf="ordre.type === 'ACHAT'">Max</div>
                    <div class="price-value">
                      <span *ngIf="ordre.type === 'VENTE'">{{ ordre.prixMin | number:'1.2-2' }}</span>
                      <span *ngIf="ordre.type === 'ACHAT'">{{ ordre.prixMax | number:'1.2-2' }}</span>
                      <span class="currency">TND</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="all-or-nothing-cell">
                <div class="cell-content">
                  <div class="tor-indicator" [class.active]="ordre.toutOuRien">
                    <i class="pi" [ngClass]="ordre.toutOuRien ? 'pi-exclamation-triangle' : 'pi-minus'"></i>
                    <span>{{ ordre.toutOuRien ? 'OUI' : 'NON' }}</span>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>

    <div *ngIf="activeItem.label === 'Ordres exécuté'" class="content-section">
      <div class="table-container">
        <p-table [value]="ordresExecute" [paginator]="true" [rows]="10" filterDisplay="row" responsiveLayout="scroll"
          styleClass="ultra-modern-table" [loading]="false" [scrollable]="false">

          <ng-template pTemplate="header">
            <tr class="table-header-row">
              <th pSortableColumn="actionNom" class="action-column">
                <div class="header-cell">
                  <i class="pi pi-building header-icon"></i>
                  <span class="header-text">Action</span>
                  <p-columnFilter field="actionNom" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="investisseurUsername" class="investor-column">
                <div class="header-cell">
                  <i class="pi pi-user header-icon"></i>
                  <span class="header-text">Investisseur</span>
                  <p-columnFilter field="investisseurUsername" matchMode="contains" display="menu"></p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="type" class="type-column">
                <div class="header-cell">
                  <i class="pi pi-tag header-icon"></i>
                  <span class="header-text">Type</span>
                  <p-columnFilter field="type" type="text" matchMode="equals" display="menu">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-dropdown [options]="[{label:'ACHAT', value:'ACHAT'}, {label:'VENTE', value:'VENTE'}]"
                        [ngModel]="value" (onChange)="filter($event.value)" placeholder="Type">
                      </p-dropdown>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th pSortableColumn="dateDeValidite" class="date-column">
                <div class="header-cell">
                  <i class="pi pi-calendar header-icon"></i>
                  <span class="header-text">Validité</span>
                  <p-columnFilter field="dateDeValidite" display="menu" [showOperator]="false" matchMode="between"
                    dataType="date">
                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                      <p-calendar selectionMode="range" [ngModel]="value" (onSelect)="filter($event)"
                        dateFormat="dd/mm/yy" placeholder="Plage de dates" showIcon="true" class="date-range-filter">
                      </p-calendar>
                    </ng-template>
                  </p-columnFilter>
                </div>
              </th>
              <th class="quantity-column">
                <div class="header-cell">
                  <i class="pi pi-hashtag header-icon"></i>
                  <span class="header-text">Quantité</span>
                </div>
              </th>
              <th class="price-column">
                <div class="header-cell">
                  <i class="pi pi-dollar header-icon"></i>
                  <span class="header-text">Prix</span>
                </div>
              </th>
              <th class="all-or-nothing-column">
                <div class="header-cell">
                  <i class="pi pi-exclamation-triangle header-icon"></i>
                  <span class="header-text">T.O.R</span>
                </div>
              </th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-ordre let-rowIndex="rowIndex">
            <tr class="table-row">
              <td class="action-cell">
                <div class="cell-content">
                  <div class="primary-text">{{ ordre.actionNom }}</div>
                  <div class="secondary-text">{{ ordre.actionIsin }}</div>
                </div>
              </td>
              <td class="investor-cell">
                <div class="cell-content">
                  <div class="investor-info">
                    <div class="investor-avatar">
                      <i class="pi pi-user"></i>
                    </div>
                    <div class="investor-details">
                      <div class="investor-name">{{ ordre.investisseurUsername }}</div>
                    </div>
                  </div>
                </div>
              </td>
              <td class="type-cell">
                <div class="cell-content">
                  <div class="type-badge" [ngClass]="'type-' + ordre.type.toLowerCase()">
                    <i class="pi" [ngClass]="ordre.type === 'ACHAT' ? 'pi-arrow-up' : 'pi-arrow-down'"></i>
                    <span>{{ ordre.type }}</span>
                  </div>
                </div>
              </td>
              <td class="date-cell">
                <div class="cell-content">
                  <div class="date-info">
                    <i class="pi pi-calendar"></i>
                    <span>{{ ordre.dateDeValidite | date:'dd/MM/yyyy' }}</span>
                  </div>
                </div>
              </td>
              <td class="quantity-cell">
                <div class="cell-content">
                  <div class="quantity-badge">
                    <i class="pi pi-hashtag"></i>
                    <span class="quantity-number">{{ ordre.quantite }}</span>
                  </div>
                </div>
              </td>
              <td class="price-cell">
                <div class="cell-content">
                  <div class="price-info">
                    <div class="price-label">Plage</div>
                    <div class="price-value">
                      <span>{{ ordre.prixMin | number:'1.2-2' }} - {{ ordre.prixMax | number:'1.2-2' }}</span>
                      <span class="currency">TND</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="all-or-nothing-cell">
                <div class="cell-content">
                  <div class="tor-indicator" [class.active]="ordre.toutOuRien">
                    <i class="pi" [ngClass]="ordre.toutOuRien ? 'pi-exclamation-triangle' : 'pi-minus'"></i>
                    <span>{{ ordre.toutOuRien ? 'OUI' : 'NON' }}</span>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>

  <!-- Popup ultra-moderne pour traitement des opérations -->
  <p-dialog [(visible)]="showTraitementPopup" [modal]="true" [style]="{ width: '50rem' }"
    [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }" [maximizable]="true" [draggable]="true" [resizable]="true"
    styleClass="modern-dialog">

    <!-- En-tête personnalisé avec titre et switcher -->
    <ng-template pTemplate="header">
      <div class="custom-header">
        <div class="header-title">
          <i class="pi pi-cog me-2"></i>
          <span>Traitement des ordres</span>
        </div>
        <div class="mode-switcher">
          <span class="mode-label" [class.active]="modeGrouper">Grouper</span>
          <p-inputSwitch [(ngModel)]="modeGrouper" class="custom-switch">
          </p-inputSwitch>
          <span class="mode-label" [class.active]="!modeGrouper">Splitter</span>
        </div>
      </div>
    </ng-template>

    <div class="modern-popup-content">

      <!-- Description compacte du mode -->
      <div class="mode-info-banner">
        <div *ngIf="modeGrouper" class="mode-info grouper">
          <i class="pi pi-objects-column me-2"></i>
          <span><strong>Mode Grouper :</strong> Les ordres sélectionnés seront regroupés en une seule opération pour
            optimiser l'exécution.</span>
        </div>
        <div *ngIf="!modeGrouper" class="mode-info splitter">
          <i class="pi pi-copy me-2"></i>
          <span><strong>Mode Splitter :</strong> Les ordres sélectionnés seront divisés en plusieurs sous-opérations
            pour
            une exécution flexible.</span>
        </div>
      </div>

      <!-- PickList pour drag and drop des ordres (Mode Grouper uniquement) -->
      <div *ngIf="modeGrouper" class="picklist-section">
        <div class="picklist-header">
          <h5>
            <i class="pi pi-sort-alt me-2"></i>
            Organisez vos ordres par glisser-déposer
          </h5>
          <div class="quantity-display">
            <span class="quantity-badge">
              <i class="pi pi-calculator me-1"></i>
              Quantité sélectionnée: <strong>{{ quantiteTotalePickList }}</strong>
            </span>
          </div>
        </div>

        <p-pickList [source]="ordresDisponibles" [target]="ordresSelectionnesPickList" sourceHeader="Ordres disponibles"
          targetHeader="Ordres pour l'opération" [dragdrop]="true" [responsive]="true" [showSourceControls]="true"
          [showTargetControls]="true" filterBy="actionNom,investisseurUsername"
          sourceFilterPlaceholder="Rechercher un ordre..." targetFilterPlaceholder="Rechercher un ordre..."
          (onMoveToTarget)="onPickListChange($event)" (onMoveToSource)="onPickListChange($event)"
          styleClass="custom-picklist" breakpoint="960px">

          <ng-template pTemplate="item" let-ordre>
            <div class="ordre-item">
              <div class="ordre-header">
                <div class="ordre-badge" [ngClass]="'badge-' + ordre.type.toLowerCase()">
                  {{ ordre.type }}
                </div>
                <div class="ordre-id">
                  #{{ ordre.id.substring(0, 8) }}...
                </div>
              </div>
              <div class="ordre-details">
                <div class="detail-row">
                  <span class="label">Action:</span>
                  <span class="value">{{ ordre.actionNom }} ({{ ordre.actionIsin }})</span>
                </div>
                <div class="detail-row">
                  <span class="label">Investisseur:</span>
                  <span class="value">{{ ordre.investisseurUsername }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">Quantité:</span>
                  <span class="value highlight">{{ ordre.quantite }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">Prix:</span>
                  <span class="value" *ngIf="ordre.type === 'VENTE'">{{ ordre.prixMin }}TND</span>
                  <span class="value" *ngIf="ordre.type === 'ACHAT'">{{ ordre.prixMax }}TND</span>
                </div>
                <div class="detail-row">
                  <span class="label">Validité:</span>
                  <span class="value">{{ ordre.dateDeValidite | date:'dd/MM/yyyy' }}</span>
                </div>
                <div class="detail-row" *ngIf="ordre.toutOuRien">
                  <span class="tout-ou-rien">
                    <i class="pi pi-exclamation-triangle me-1"></i>
                    Tout ou rien
                  </span>
                </div>
              </div>
            </div>
          </ng-template>
        </p-pickList>
      </div>

      <!-- PickList pour les opérations fractionnées (Mode Splitter) -->
      <div *ngIf="!modeGrouper" class="picklist-section">
        <div class="picklist-header">
          <h5>
            <i class="pi pi-copy me-2"></i>
            Simulation du fractionnement
          </h5>
          <div class="quantity-display">
            <span class="quantity-badge">
              <i class="pi pi-calculator me-1"></i>
              Quantité sélectionnée: <strong>{{ getQuantiteTotaleOperations() }}</strong>
            </span>
          </div>
        </div>

        <!-- Loading de la simulation -->
        <div *ngIf="loadingSimulation" class="loading-simulation">
          <i class="pi pi-spin pi-spinner"></i>
          <span>Simulation du fractionnement en cours...</span>
        </div>

        <!-- PickList des opérations avec le même style que les ordres -->
        <div *ngIf="!loadingSimulation && operationsSimulees.length > 0">
          <p-pickList [source]="operationsSimulees" [target]="operationsSelectionnees"
            sourceHeader="Opérations simulées" targetHeader="Opération à envoyer en premier" [dragdrop]="true"
            [responsive]="true" [showSourceControls]="true" [showTargetControls]="true"
            filterBy="quantiteTotale,montantTotal" sourceFilterPlaceholder="Rechercher une opération..."
            targetFilterPlaceholder="Rechercher une opération..." (onMoveToTarget)="onOperationsPickListChange($event)"
            (onMoveToSource)="onOperationsPickListChange($event)" styleClass="custom-picklist" breakpoint="960px">

            <!-- Template unifié pour les opérations (même style que les ordres) -->
            <ng-template pTemplate="item" let-operation let-index="index">
              <div class="ordre-item">
                <div class="ordre-header">
                  <div class="ordre-badge badge-operation">
                    <i class="pi pi-cog me-1"></i>
                    Op. {{ index + 1 }}
                  </div>
                  <div class="ordre-id">{{ operation.statut }}</div>
                </div>
                <div class="ordre-details">
                  <div class="detail-row">
                    <span class="label">Quantité:</span>
                    <span class="value highlight">{{ operation.quantiteTotale }}</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Montant:</span>
                    <span class="value">{{ operation.montantTotal | number:'1.2-2' }}TND</span>
                  </div>
                  <div class="detail-row">
                    <span class="label">Commission:</span>
                    <span class="value">{{ operation.montantTotal * 0.004 | number:'1.2-2' }}TND</span>
                  </div>
                </div>
              </div>
            </ng-template>
          </p-pickList>
        </div>

        <!-- Message si aucune opération simulée -->
        <div *ngIf="!loadingSimulation && operationsSimulees.length === 0" class="no-operations">
          <i class="pi pi-exclamation-triangle"></i>
          <span>Aucune opération simulée</span>
        </div>
      </div>

      <!-- Boutons d'action -->
      <div class="popup-actions">
        <button type="button" class="btn-cancel" (click)="annulerTraitement()">
          <i class="pi pi-times me-2"></i>
          Annuler
        </button>
        <button *ngIf="modeGrouper" type="button" class="btn-generate"
          [disabled]="ordresSelectionnesPickList.length === 0" (click)="genererOperation()">
          <i class="pi pi-cog me-2"></i>
          Générer l'opération
        </button>
        <button *ngIf="!modeGrouper" type="button" class="btn-generate"
          [disabled]="operationsSelectionnees.length === 0" (click)="genererOperation()">
          <i class="pi pi-send me-2"></i>
          Valider et envoyer
        </button>
      </div>
    </div>
  </p-dialog>

  <!-- Popup de confirmation pour génération d'opération -->
  <p-dialog [(visible)]="showConfirmationPopup" [modal]="true" [closable]="false" [draggable]="false"
    [resizable]="false" styleClass="confirmation-dialog" [style]="{width: '500px'}" header="">

    <div class="confirmation-content">
      <!-- En-tête de confirmation -->
      <div class="confirmation-header">
        <div class="confirmation-icon">
          <i class="pi pi-exclamation-triangle"></i>
        </div>
        <h3>Confirmer la génération d'opération</h3>
      </div>

      <!-- Détails de l'opération -->
      <div class="confirmation-details">
        <div class="detail-item">
          <span class="label">Mode sélectionné :</span>
          <span class="value mode-badge" [ngClass]="modeGrouper ? 'grouper' : 'splitter'">
            <i class="pi" [ngClass]="modeGrouper ? 'pi-objects-column' : 'pi-copy'"></i>
            {{ modeGrouper ? 'Grouper' : 'Splitter' }}
          </span>
        </div>
        <!-- Détails pour le mode Grouper -->
        <div *ngIf="modeGrouper" class="detail-item">
          <span class="label">Nombre d'ordres :</span>
          <span class="value">{{ ordresSelectionnesPickList.length }}</span>
        </div>
        <div *ngIf="modeGrouper" class="detail-item">
          <span class="label">Quantité totale :</span>
          <span class="value highlight">{{ quantiteTotalePickList }}</span>
        </div>
        <div *ngIf="modeGrouper && ordresSelectionnesPickList.length > 0" class="detail-item">
          <span class="label">Type d'ordre :</span>
          <span class="value">{{ ordresSelectionnesPickList[0].type }}</span>
        </div>
        <div *ngIf="modeGrouper && ordresSelectionnesPickList.length > 0" class="detail-item">
          <span class="label">Action :</span>
          <span class="value">{{ ordresSelectionnesPickList[0].actionNom }}</span>
        </div>

        <!-- Détails pour le mode Splitter -->
        <div *ngIf="!modeGrouper && ordreEnCoursDeTraitement" class="detail-item">
          <span class="label">Ordre à fractionner :</span>
          <span class="value">{{ ordreEnCoursDeTraitement.actionNom }}</span>
        </div>
        <div *ngIf="!modeGrouper && ordreEnCoursDeTraitement" class="detail-item">
          <span class="label">Quantité totale :</span>
          <span class="value highlight">{{ ordreEnCoursDeTraitement.quantite }}</span>
        </div>
        <div *ngIf="!modeGrouper && operationsSelectionnees.length > 0" class="detail-item">
          <span class="label">Opération prioritaire :</span>
          <span class="value">{{ operationsSelectionnees[0].quantiteTotale }} unités</span>
        </div>
        <div *ngIf="!modeGrouper && operationsSelectionnees.length > 0" class="detail-item">
          <span class="label">Montant prioritaire :</span>
          <span class="value">{{ operationsSelectionnees[0].montantTotal | number:'1.2-2' }}TND</span>
        </div>
        <div *ngIf="!modeGrouper && operationsSelectionnees.length > 0" class="detail-item">
          <span class="label">Commission :</span>
          <span class="value">{{ operationsSelectionnees[0].montantTotal * 0.004 | number:'1.2-2' }}TND</span>
        </div>
      </div>

      <!-- Message d'avertissement -->
      <div class="confirmation-warning">
        <i class="pi pi-info-circle me-2"></i>
        <span *ngIf="modeGrouper">Cette action va regrouper les ordres sélectionnés en une seule opération. Cette
          action
          est irréversible.</span>
        <span *ngIf="!modeGrouper">Cette action va valider le fractionnement et envoyer l'opération sélectionnée en
          premier. Les autres opérations seront créées et mises en attente. Cette action est irréversible.</span>
      </div>

      <!-- Boutons d'action -->
      <div class="confirmation-actions">
        <button type="button" class="btn-cancel-confirm" (click)="annulerGeneration()">
          <i class="pi pi-times me-2"></i>
          Annuler
        </button>
        <button type="button" class="btn-confirm-generate" (click)="confirmerGeneration()">
          <i class="pi pi-check me-2"></i>
          Confirmer et générer
        </button>
      </div>
    </div>
  </p-dialog>

</div>